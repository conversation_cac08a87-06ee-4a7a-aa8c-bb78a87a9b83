"""
URL configuration for ProyectoDjango project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include #importo include para poder incluir las urls de la app

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('Main.urls')),
    path('blog/', include ('blog.urls')), #le digo cual es la URL q desencadena el response, en este caso la raiz es nada porq es index
]                                           #una vez entra a index, ejecuta la funcion index de views, esta funcion muestra un html q recibe un contexto
                                            # q reemplazara el mensaje del html original
